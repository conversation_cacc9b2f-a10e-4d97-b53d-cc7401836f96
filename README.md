# Neo4j 知识图谱构建系统

基于 GPT-4o 和 Neo4j 的知识图谱构建系统，从 Python 编程教材中抽取实体关系并构建知识图谱。

## 🚀 功能特点

- ✅ **智能实体抽取**: 使用 GPT-4o 自动抽取实体关系
- ✅ **白名单过滤**: 严格按照预定义白名单过滤实体、标签和关系
- ✅ **自动描述生成**: LLM 自动为每个实体生成中文描述
- ✅ **批量处理**: 支持大文本文件的分块处理
- ✅ **数据完整性**: Neo4j 约束确保数据质量
- ✅ **灵活导入**: 支持 CSV 中转和直接 Neo4j 写入
- ✅ **模块化架构**: 基于 KISS 原则和关注点分离的清晰架构

## 📁 项目结构

```
gyrw-kg/
├── src/                        # 源代码目录
│   ├── core/                   # 核心业务逻辑
│   │   ├── text_processor.py   # 文本分块处理
│   │   ├── llm_client.py       # LLM调用和抽取
│   │   ├── neo4j_client.py     # Neo4j数据库操作
│   │   └── pipeline.py         # 主要业务流程
│   ├── data/                   # 数据处理模块
│   │   ├── whitelist_loader.py # 白名单加载
│   │   ├── filter.py           # 数据过滤
│   │   └── csv_handler.py      # CSV文件处理
│   ├── config/                 # 配置管理
│   │   ├── settings.py         # 配置加载和管理
│   │   └── prompts.py          # LLM提示词模板
│   └── utils/                  # 通用工具
│       └── helpers.py          # 辅助函数
├── scripts/                    # 可执行脚本
│   ├── extract_to_csv.py       # 抽取到CSV
│   ├── csv_to_neo4j.py         # CSV导入Neo4j
│   ├── pipeline.py             # 完整管道
│   └── setup_constraints.py    # 约束设置
├── data/                       # 数据文件
│   ├── whitelists/             # 白名单文件
│   ├── samples/                # 示例数据
│   └── output/                 # 输出文件
├── sql/                        # Cypher脚本
├── tests/                      # 测试文件
├── docs/                       # 文档
├── config/                     # 配置文件
└── .env                        # 环境变量
```

## 🛠️ 环境准备

### 1. 系统要求

- Neo4j 5.x + APOC 插件
- Python 3.10+
- OpenAI API 访问权限

### 2. 安装依赖

```bash
pip install -r config/requirements.txt
```

### 3. 配置环境变量

复制配置模板并编辑：

```bash
cp config/.env.example .env
```

编辑 `.env` 文件：

```bash
# Neo4j配置
NEO4J_URI="bolt://localhost:7687"
NEO4J_USER="neo4j"
NEO4J_PASSWORD="your_password"

# OpenAI配置
OPENAI_API_KEY="sk-your-api-key"
```

## 🚀 快速开始

### 方法一：推荐的两步法

#### 步骤 1: 抽取到 CSV

```bash
# 从文本抽取实体关系到CSV
python scripts/extract_to_csv.py data/samples/python第八章.txt

# 分析抽取结果
python scripts/extract_to_csv.py analyze data/output/extracted_triples.csv
```

#### 步骤 2: 导入到 Neo4j

```bash
# 设置约束（首次运行）
python scripts/setup_constraints.py

# 导入CSV到Neo4j
python scripts/csv_to_neo4j.py data/output/extracted_triples.csv --clear

# 验证数据库
python scripts/csv_to_neo4j.py verify
```

### 方法二：一步法（直接处理）

```bash
# 直接从文本到Neo4j（需要Neo4j连接正常）
python scripts/pipeline.py data/samples/python第八章.txt --clear
```

## 🧪 测试和诊断

### 连接测试

```bash
# 测试Neo4j连接
python tests/test_neo4j_connection.py

# 运行所有测试
python tests/run_tests.py
```

## 📊 白名单配置

### 实体白名单 (data/whitelists/entities.csv)

```
函数
代码块
函数体
实参
形参
...
```

### 标签白名单 (data/whitelists/labels.csv)

```
label
Concept
Function
Parameter
CodeBlock
...
```

### 关系白名单 (data/whitelists/relations.csv)

```
relation_type
contains
depends_on
is_a_type_of
part_of
...
```

## 🔧 核心组件

### 文本处理器 (TextProcessor)
- 智能文本分块（约700 tokens/块）
- 保持语义完整性
- 支持大文件流式处理

### LLM客户端 (LLMClient)
- GPT-4o-mini 实体关系抽取
- JSON 模式确保结构化输出
- 函数调用保证格式一致性

### Neo4j客户端 (Neo4jClient)
- 批量 MERGE 操作提高性能
- 约束确保数据完整性
- 连接管理和错误处理

### 数据过滤器 (DataFilter)
- 严格白名单过滤
- 数据质量验证
- 统计信息生成

## 📈 性能优化

- **流式处理**: 大文件分块处理，减少内存占用
- **批量写入**: Neo4j 批量操作提高性能
- **连接复用**: 智能连接管理
- **错误恢复**: 健壮的错误处理机制

## 🔍 故障排除

### Neo4j 连接问题

1. 运行连接诊断：`python tests/test_neo4j_connection.py`
2. 检查 Neo4j 服务状态
3. 验证端口 7687 可访问性
4. 确认用户名密码正确

### API 限制

- 使用 gpt-4o-mini 降低成本
- 实现重试机制
- 合理设置请求间隔

## 📚 开发指南

### 添加新功能

1. 在相应模块中添加功能
2. 更新相关测试
3. 更新文档

### 自定义白名单

1. 编辑 `data/whitelists/` 中的CSV文件
2. 重新运行抽取流程

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！
