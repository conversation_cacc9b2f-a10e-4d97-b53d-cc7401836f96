# Neo4j 知识图谱构建系统

基于 GPT-4o 和 Neo4j 的知识图谱构建系统，从 Python 编程教材中抽取实体关系并构建知识图谱。

## 功能特点

- ✅ **智能实体抽取**: 使用 GPT-4o 自动抽取实体关系
- ✅ **白名单过滤**: 严格按照预定义白名单过滤实体、标签和关系
- ✅ **自动描述生成**: LLM 自动为每个实体生成中文描述
- ✅ **批量处理**: 支持大文本文件的分块处理
- ✅ **数据完整性**: Neo4j 约束确保数据质量
- ✅ **灵活导入**: 支持 CSV 中转和直接 Neo4j 写入

## 项目结构

```
gyrw-kg/
├── whitelists/              # 白名单文件
│   ├── entities.csv         # 实体白名单（无header）
│   ├── labels.csv          # 标签白名单
│   └── relations.csv       # 关系白名单
├── testpy/                 # 测试脚本文件夹
│   ├── test_neo4j_connection.py  # 连接诊断工具
│   ├── test_pipeline.py    # 管道测试脚本
│   ├── run_tests.py        # 综合测试脚本
│   └── README.md           # 测试说明文档
├── utils.py                # 核心工具函数
├── pipeline.py             # 主管道脚本
├── extract_to_csv.py       # 抽取到CSV（推荐）
├── csv_to_neo4j.py         # CSV导入Neo4j
├── setup_constraints.py    # 约束设置
├── cleaning.cql           # 数据清洗脚本
├── cy_constraints.cql     # 约束创建脚本
├── python第八章.txt        # 示例文本文件
├── requirements.txt        # Python依赖
├── README.md              # 项目文档
├── 项目完成总结.md         # 完成总结
├── neo4j-txt-to-database.md # 原始需求文档
└── .env                   # 环境配置
```

## 快速开始

### 1. 环境准备

确保已安装：

- Neo4j 5.x + APOC 插件
- Python 3.10+
- 所需 Python 包（见 requirements.txt）

### 2. 配置环境变量

编辑`.env`文件：

```bash
# Neo4j配置
NEO4J_URI="bolt://localhost:7687"
NEO4J_USER="neo4j"
NEO4J_PASSWORD="your_password"

# OpenAI配置
OPENAI_API_KEY="sk-your-api-key"
```

### 3. 推荐流程（两步法）

#### 步骤 1: 抽取到 CSV

```bash
# 从文本抽取实体关系到CSV
python extract_to_csv.py python第八章.txt

# 分析抽取结果
python extract_to_csv.py analyze extracted_triples.csv
```

#### 步骤 2: 导入到 Neo4j

```bash
# 设置约束（首次运行）
python setup_constraints.py

# 导入CSV到Neo4j
python csv_to_neo4j.py extracted_triples.csv --clear

# 验证数据库
python csv_to_neo4j.py verify
```

### 4. 一步法（直接处理）

```bash
# 直接从文本到Neo4j（需要Neo4j连接正常）
python pipeline.py python第八章.txt
```

## 白名单配置

### entities.csv（无 header）

```
函数
代码块
函数体
实参
形参
...
```

### labels.csv

```
label
Concept
Function
Parameter
CodeBlock
...
```

### relations.csv

```
relation_type
contains
depends_on
is_a_type_of
part_of
...
```

## 核心功能

### 文本分块

- 自动将大文本分割成约 700 tokens 的小块
- 保持语义完整性

### LLM 抽取

- 使用 GPT-4o-mini 进行实体关系抽取
- JSON 模式确保结构化输出
- 函数调用保证格式一致性

### 白名单过滤

- 只保留白名单中的实体
- 标签和关系严格受控
- LLM 自动生成描述

### Neo4j 集成

- 批量 MERGE 操作提高性能
- 约束确保数据完整性
- APOC 支持大规模数据处理

## 数据质量保证

### 约束设置

- 每个标签的 name 字段唯一性约束
- 每个节点必须有 description 属性
- 自动验证数据完整性

### 清洗机制

- 使用 APOC 批量删除无效数据
- 白名单严格过滤
- 数据质量检查

## 示例输出

抽取的三元组示例：

```
函数 (Function) -> is_a_type_of -> 代码块 (CodeBlock)
  头实体描述: 带名字的代码块
  尾实体描述: 用于完成具体工作的代码块

形参 (Parameter) -> part_of -> 函数 (Function)
  头实体描述: 函数定义中需要的信息
  尾实体描述: 完成工作所需的信息
```

## 故障排除

### Neo4j 连接问题

1. 确认 Neo4j 服务正在运行
2. 检查密码是否正确
3. 验证端口 7687 是否可访问
4. 运行 `python testpy/test_neo4j_connection.py` 进行诊断
5. 使用 CSV 中转方式绕过连接问题

### API 限制

- 使用 gpt-4o-mini 降低成本
- 实现重试机制
- 合理设置请求间隔

### 内存优化

- 流式处理大文件
- 批量写入减少内存占用
- 及时释放资源

## 扩展功能

### 自定义白名单

- 根据领域需求调整实体白名单
- 扩展标签和关系类型
- 支持多语言实体

### 批量处理

- 支持多文件批量处理
- 并行处理提高效率
- 进度跟踪和错误恢复

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！
