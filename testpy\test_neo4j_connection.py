#!/usr/bin/env python3
"""
Neo4j连接测试脚本
详细测试Neo4j连接并输出调试信息
"""

import os
import sys
from neo4j import GraphDatabase
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def test_connection_detailed():
    """详细测试Neo4j连接"""
    print("=== Neo4j连接详细测试 ===")

    # 重新加载环境变量
    load_dotenv(override=True)

    # 1. 显示环境变量
    print("\n1. 环境变量检查:")
    uri = os.getenv("NEO4J_URI", "bolt://localhost:7687")
    user = os.getenv("NEO4J_USER", "neo4j")
    password = os.getenv("NEO4J_PASSWORD")
    database = os.getenv("NEO4J_DATABASE", "neo4j")

    # 强制使用正确的URI
    uri = "bolt://localhost:7687"
    
    print(f"   NEO4J_URI: {uri}")
    print(f"   NEO4J_USER: {user}")
    print(f"   NEO4J_PASSWORD: {'*' * len(password) if password else 'NOT SET'}")
    print(f"   NEO4J_DATABASE: {database}")
    
    if not password:
        print("❌ NEO4J_PASSWORD未设置")
        return False
    
    # 2. 尝试创建驱动
    print("\n2. 创建驱动连接:")
    try:
        driver = GraphDatabase.driver(uri, auth=(user, password))
        print("✓ 驱动创建成功")
    except Exception as e:
        print(f"❌ 驱动创建失败: {e}")
        return False
    
    # 3. 测试连接
    print("\n3. 测试数据库连接:")
    try:
        # 验证连接
        driver.verify_connectivity()
        print("✓ 连接验证成功")
    except Exception as e:
        print(f"❌ 连接验证失败: {e}")
        driver.close()
        return False
    
    # 4. 执行简单查询
    print("\n4. 执行测试查询:")
    try:
        with driver.session(database=database) as session:
            # 测试查询1: 返回简单值
            result = session.run("RETURN 'Hello Neo4j!' as message, datetime() as current_time")
            record = result.single()
            print(f"✓ 测试消息: {record['message']}")
            print(f"✓ 服务器时间: {record['current_time']}")
            
            # 测试查询2: 数据库信息
            result = session.run("CALL db.info()")
            info = result.single()
            if info:
                print(f"✓ 数据库名称: {info.get('name', 'N/A')}")
                print(f"✓ 数据库ID: {info.get('id', 'N/A')}")
            
    except Exception as e:
        print(f"❌ 查询执行失败: {e}")
        driver.close()
        return False
    
    # 5. 检查数据库状态
    print("\n5. 数据库状态检查:")
    try:
        with driver.session(database=database) as session:
            # 节点和关系统计
            result = session.run("""
                MATCH (n) 
                OPTIONAL MATCH (n)-[r]-()
                RETURN count(DISTINCT n) as nodes, count(DISTINCT r) as relationships
            """)
            stats = result.single()
            print(f"✓ 当前节点数: {stats['nodes']}")
            print(f"✓ 当前关系数: {stats['relationships']}")
            
            # 检查约束
            try:
                result = session.run("SHOW CONSTRAINTS")
                constraints = list(result)
                print(f"✓ 约束数量: {len(constraints)}")
                if constraints:
                    print("   约束列表:")
                    for i, constraint in enumerate(constraints[:5]):  # 只显示前5个
                        name = constraint.get('name', f'constraint_{i}')
                        print(f"     - {name}")
            except Exception as e:
                print(f"⚠️  无法获取约束信息: {e}")
            
            # 检查索引
            try:
                result = session.run("SHOW INDEXES")
                indexes = list(result)
                print(f"✓ 索引数量: {len(indexes)}")
            except Exception as e:
                print(f"⚠️  无法获取索引信息: {e}")
                
    except Exception as e:
        print(f"❌ 状态检查失败: {e}")
        driver.close()
        return False
    
    # 6. 测试写入权限
    print("\n6. 测试写入权限:")
    try:
        with driver.session(database=database) as session:
            # 创建测试节点
            session.run("CREATE (test:TestNode {name: 'connection_test', timestamp: datetime()})")
            print("✓ 测试节点创建成功")
            
            # 查询测试节点
            result = session.run("MATCH (test:TestNode {name: 'connection_test'}) RETURN test.timestamp as timestamp")
            record = result.single()
            if record:
                print(f"✓ 测试节点查询成功: {record['timestamp']}")
            
            # 删除测试节点
            session.run("MATCH (test:TestNode {name: 'connection_test'}) DELETE test")
            print("✓ 测试节点删除成功")
            
    except Exception as e:
        print(f"❌ 写入权限测试失败: {e}")
        driver.close()
        return False
    
    # 7. 测试APOC可用性
    print("\n7. 测试APOC插件:")
    try:
        with driver.session(database=database) as session:
            result = session.run("RETURN apoc.version() as version")
            record = result.single()
            if record:
                print(f"✓ APOC版本: {record['version']}")
            else:
                print("⚠️  APOC可能未安装或未启用")
    except Exception as e:
        print(f"⚠️  APOC测试失败: {e}")
    
    # 关闭连接
    driver.close()
    print("\n✅ 所有连接测试完成！")
    return True

def test_different_configs():
    """测试不同的连接配置"""
    print("\n=== 测试不同连接配置 ===")
    
    configs = [
        {
            "name": "默认配置",
            "uri": "bolt://localhost:7687",
            "user": "neo4j",
            "password": os.getenv("NEO4J_PASSWORD")
        },
        {
            "name": "明确指定数据库",
            "uri": "bolt://localhost:7687",
            "user": "neo4j", 
            "password": os.getenv("NEO4J_PASSWORD"),
            "database": "neo4j"
        },
        {
            "name": "Neo4j+s协议",
            "uri": "neo4j://localhost:7687",
            "user": "neo4j",
            "password": os.getenv("NEO4J_PASSWORD")
        }
    ]
    
    for config in configs:
        print(f"\n测试配置: {config['name']}")
        print(f"URI: {config['uri']}")
        
        try:
            driver = GraphDatabase.driver(config["uri"], auth=(config["user"], config["password"]))
            
            database = config.get("database", "neo4j")
            with driver.session(database=database) as session:
                result = session.run("RETURN 1 as test")
                record = result.single()
                if record and record["test"] == 1:
                    print(f"✓ {config['name']} - 连接成功")
                else:
                    print(f"❌ {config['name']} - 查询失败")
            
            driver.close()
            
        except Exception as e:
            print(f"❌ {config['name']} - 失败: {e}")

def main():
    """主函数"""
    print("Neo4j连接诊断工具")
    print("=" * 50)
    
    # 主要连接测试
    success = test_connection_detailed()
    
    if not success:
        print("\n主要连接测试失败，尝试其他配置...")
        test_different_configs()
    
    print(f"\n{'='*50}")
    print("诊断完成")
    
    if success:
        print("🎉 Neo4j连接正常，可以继续使用系统！")
    else:
        print("❌ Neo4j连接有问题，请检查:")
        print("   1. Neo4j服务是否运行")
        print("   2. 端口7687是否开放")
        print("   3. 用户名密码是否正确")
        print("   4. 数据库是否存在")

if __name__ == "__main__":
    main()
